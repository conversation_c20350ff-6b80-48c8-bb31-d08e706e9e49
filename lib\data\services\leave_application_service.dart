import 'package:http/http.dart' as http;
import '../models/leave_application_list_model.dart';
import '/utils/constants.dart';
import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/leave_application_request_model.dart';
import '../../utils/ui_helper.dart';
import '../models/admin_leave_application_response_model.dart';

class LeaveApplicationService {
  Future<List<LeaveApplicationList>> getLeaveApplicationList(
    String token,
    Future<bool> Function() refreshToken,
  ) async {
    if (token.isEmpty) {
      throw Exception('No authentication token available');
    }

    try {
      var response = await http
          .get(
            Uri.parse(leaveApplication),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw TimeoutException('Request timed out');
            },
          );

      if (response.statusCode == 401) {
        final refreshSuccess = await refreshToken();
        if (!refreshSuccess) {
          throw Exception('Session expired. Please login again.');
        }

        // Get the new token
        token = await _getNewToken();

        // Retry with new token
        response = await http.get(
          Uri.parse(leaveApplication),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        );
      }

      if (response.statusCode != 200) {
        throw Exception(
          'Failed to load leave application list: ${response.statusCode}',
        );
      }

      final responseData = jsonDecode(response.body);

      if (responseData['status'] != 'success') {
        throw Exception(
          'Failed to load leave application list: ${responseData['message'] ?? 'Unknown error'}',
        );
      }

      final List<dynamic> leaveApplicationData = responseData['data'];
      return leaveApplicationData
          .map((json) => LeaveApplicationList.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to load leave application list: $e');
    }
  }

  Future<bool> submitLeaveApplication(
    String token,
    LeaveApplicationRequest leaveApplicationRequest,
    Future<bool> Function() refreshToken,
  ) async {
    if (token.isEmpty) {
      throw Exception('No authentication token available');
    }

    try {
      final response = await http
          .post(
            Uri.parse(leaveApplication),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: jsonEncode(leaveApplicationRequest.toJson()),
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw TimeoutException('Request timed out');
            },
          );

      if (response.statusCode != 201) {
        final responseData = jsonDecode(response.body);
        final errorMessage =
            responseData['message'] ?? 'Unknown error occurred';
        throw Exception('Failed to submit leave application: $errorMessage');
      }

      final responseData = jsonDecode(response.body);
      if (responseData['status'] != 'success') {
        throw Exception(
          'Failed to submit leave application: ${responseData['message'] ?? 'Unknown error'}',
        );
      }

      _safeShowSnackBar(
        'Leave application submitted successfully',
        isError: false,
      );
      return true;
    } on TimeoutException {
      throw Exception('Request timed out. Please try again.');
    } on FormatException {
      throw Exception('Invalid response from server');
    } catch (e) {
      throw Exception('Failed to submit leave application: $e');
    }
  }

  /// Helper method to get the new token from SharedPreferences
  Future<String> _getNewToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token') ?? '';
  }

  // Helper method to safely show snackbars using UIHelper
  void _safeShowSnackBar(String message, {bool isError = true}) {
    uiHelper.showSnackBar(message, isError: isError);
  }
}
