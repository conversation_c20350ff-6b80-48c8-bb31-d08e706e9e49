import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../controllers/auth_controller.dart';
import '../widgets/profile_header.dart';
import '../widgets/profile_info_section.dart';
import '../widgets/profile_info_row.dart';
import '../../utils/app_theme.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final user = context.watch<AuthController>().currentUser;

    if (user == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Profile'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.person_off_outlined,
                size: 64,
                color: AppTheme.textLightColor,
              ),
              const SizedBox(height: 16),
              Text(
                'User data not available',
                style: TextStyle(
                  fontSize: 18,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Profile header with user info
            ProfileHeader(user: user),

            // Profile details sections
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Personal information section
                  ProfileInfoSection(
                    title: 'Personal Information',
                    icon: Icons.person,
                    children: [
                      ProfileInfoRow(
                        label: 'Email',
                        value: user.email,
                        icon: Icons.email_outlined,
                      ),
                      ProfileInfoRow(
                        label: 'Department',
                        value: user.department,
                        icon: Icons.business_outlined,
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Work location section
                  ProfileInfoSection(
                    title: 'Work Location',
                    icon: Icons.location_on,
                    children: [
                      ProfileInfoRow(
                        label: 'Location Name',
                        value: user.allowedLocation.name,
                        icon: Icons.place_outlined,
                      ),
                      ProfileInfoRow(
                        label: 'Address',
                        value: user.allowedLocation.address,
                        icon: Icons.home_outlined,
                      ),
                      ProfileInfoRow(
                        label: 'Working Hours',
                        value: '${user.allowedLocation.workingHours.start} - ${user.allowedLocation.workingHours.end}',
                        icon: Icons.access_time,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
