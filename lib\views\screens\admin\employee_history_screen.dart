import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../widgets/admin/employee_card.dart';
import '../../../data/models/attendance_filter_model.dart';
import '../../../data/models/attendance_history_model.dart';
import '../../../data/services/attendance_service.dart';
import '../../../controllers/auth_controller.dart';
import '../../../utils/snackbar_helper.dart';
import '../../../utils/app_theme.dart';
import 'package:intl/intl.dart';

class EmployeeHistoryScreen extends StatefulWidget {
  const EmployeeHistoryScreen({super.key});

  @override
  State<EmployeeHistoryScreen> createState() => _EmployeeHistoryScreenState();
}

class _EmployeeHistoryScreenState extends State<EmployeeHistoryScreen> {
  final AttendanceFilter _filter = AttendanceFilter();
  final AttendanceService _attendanceService = AttendanceService();
  List<AttendanceHistory> attendanceList = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _setTodayDate();
    _fetchAttendanceHistory();
  }

  void _setTodayDate() {
    final today = DateTime.now();
    final formattedDate = DateFormat('yyyy-MM-dd').format(today);
    _filter.dateFrom = formattedDate;
    _filter.dateTo = formattedDate;
  }

  Future<void> _fetchAttendanceHistory() async {
    setState(() => isLoading = true);

    try {
      final authController = Provider.of<AuthController>(
        context,
        listen: false,
      );
      final token = authController.token;

      if (token == null) {
        throw Exception('Not authenticated');
      }

      final attendanceHistory = await _attendanceService.getAttendanceHistory(
        context,
        _filter,
      );

      if (mounted) {
        setState(() {
          attendanceList = attendanceHistory;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        SnackBarHelper.showErrorSnackBar(context, e.toString());
        setState(() => isLoading = false);
      }
    }
  }

  Future<void> _refreshAttendanceHistory() async {
    try {
      final authController = Provider.of<AuthController>(
        context,
        listen: false,
      );
      final token = authController.token;

      if (token == null) {
        throw Exception('Not authenticated');
      }

      final attendanceHistory = await _attendanceService.getAttendanceHistory(
        context,
        _filter,
      );

      if (mounted) {
        setState(() {
          attendanceList = attendanceHistory;
        });
      }
    } catch (e) {
      if (mounted) {
        SnackBarHelper.showErrorSnackBar(context, e.toString());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          "Today's Attendance",
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: RefreshIndicator(
        onRefresh: _refreshAttendanceHistory,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child:
                isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : attendanceList.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.history,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No attendance records for today',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    )
                    : ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: attendanceList.length,
                      itemBuilder: (context, index) {
                        return EmployeeAttendanceCard(
                          attendance: attendanceList[index],
                        );
                      },
                    ),
          ),
        ),
      ),
    );
  }
}
