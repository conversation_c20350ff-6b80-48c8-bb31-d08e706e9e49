class LeaveApplicationList {
  final String id;
  final int userId;
  final String name;
  final String leaveType;
  final String leaveFrom;
  final String leaveTo;
  final int leaveDays;
  final String leaveReason;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  LeaveApplicationList({
    required this.id,
    required this.userId,
    required this.name,
    required this.leaveType,
    required this.leaveFrom,
    required this.leaveTo,
    required this.leaveDays,
    required this.leaveReason,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory LeaveApplicationList.fromJson(Map<String, dynamic> json) {
    return LeaveApplicationList(
      id: json['id'].toString(),
      userId: _parseToInt(json['user_id']) ?? 0,
      name: json['name'] ?? 'Unknown',
      leaveType: json['leave_type'] ?? 'Unknown',
      leaveFrom: json['start_date'] ?? '',
      leaveTo: json['end_date'] ?? '',
      leaveDays: _parseToInt(json['days_count']) ?? 0,
      leaveReason: json['reason'] ?? 'No reason provided',
      status: json['leave_status'],
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'])
              : null,
    );
  }

  static int? _parseToInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    return null;
  }
}
