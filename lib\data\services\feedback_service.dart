import 'package:http/http.dart' as http;
import '../models/feedback_list_model.dart';
import '/utils/constants.dart';
import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/feedback_request_model.dart';
import '../../utils/ui_helper.dart';
import '../models/admin_feedback_response_model.dart';
class FeedbackService {
  Future<List<FeedbackList>> getFeedbackList(
    String token,
    Future<bool> Function() refreshToken,
  ) async {
    if (token.isEmpty) {
      throw Exception('No authentication token available');
    }

    try {
      var response = await http
          .get(
            Uri.parse(feedback),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw TimeoutException('Request timed out');
            },
          );

      if (response.statusCode == 401) {
        final refreshSuccess = await refreshToken();
        if (!refreshSuccess) {
          throw Exception('Session expired. Please login again.');
        }

        // Get the new token
        token = await _getNewToken();

        // Retry with new token
        response = await http.get(
          Uri.parse(feedback),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        );
      }

      if (response.statusCode != 200) {
        throw Exception('Failed to load feedback list: ${response.statusCode}');
      }

      final responseData = jsonDecode(response.body);

      if (responseData['status'] != 'success') {
        throw Exception(
          'Failed to load feedback list: ${responseData['message'] ?? 'Unknown error'}',
        );
      }

      final List<dynamic> feedbackData = responseData['data'];
      return feedbackData.map((json) => FeedbackList.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to load feedback list: $e');
    }
  }

  Future<bool> submitFeedback(
    String token,
    FeedbackRequest feedbackRequest,
  ) async {
    try {
      final response = await http
          .post(
            Uri.parse(feedback),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: jsonEncode(feedbackRequest.toJson()),
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw TimeoutException('Request timed out');
            },
          );

      if (response.statusCode != 201) {
        final responseData = jsonDecode(response.body);
        final errorMessage =
            responseData['message'] ?? 'Unknown error occurred';
        throw Exception('Failed to submit feedback: $errorMessage');
      }

      final responseData = jsonDecode(response.body);
      if (responseData['status'] != 'success') {
        throw Exception(responseData['message'] ?? 'Failed to submit feedback');
      }

      _safeShowSnackBar('Feedback submitted successfully', isError: false);
      return true;
    } on TimeoutException {
      throw Exception('Request timed out. Please try again.');
    } on FormatException {
      throw Exception('Invalid response from server');
    } catch (e) {
      throw Exception('Failed to submit feedback: ${e.toString()}');
    }
  }

  Future<bool> updateFeedback(
    String token,
    String feedbackId,
    FeedbackRequest feedbackRequest,
  ) async {
    try {

      final response = await http
          .put(
            Uri.parse('$feedback/$feedbackId'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: jsonEncode(feedbackRequest.toJson()),
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw TimeoutException('Request timed out');
            },
          );

      if (response.statusCode == 401) {
        throw Exception('Session expired. Please login again.');
      }

      if (response.statusCode != 200) {
        try {
          final responseData = jsonDecode(response.body);
          final errorMessage =
              responseData['message'] ?? 'Unknown error occurred';
          throw Exception('Failed to update feedback: $errorMessage');
        } catch (e) {
          throw Exception(
            'Failed to update feedback: ${response.statusCode} - ${response.body}',
          );
        }
      }

      try {
        final responseData = jsonDecode(response.body);
        if (responseData['status'] != 'success') {
          throw Exception(
            responseData['message'] ?? 'Failed to update feedback',
          );
        }
      } catch (e) {
        // If we can't parse the response, but got a 200 status code, consider it a success
      }

      return true;
    } on TimeoutException {
      throw Exception('Request timed out. Please try again.');
    } on FormatException {
      throw Exception('Invalid response from server');
    } catch (e) {
      throw Exception('Failed to update feedback: ${e.toString()}');
    }
  }

  Future<bool> submitResponse(
    String token,
    String feedbackId,
    AdminFeedbackResponse adminFeedbackResponse,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$responseFeedback/$feedbackId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(adminFeedbackResponse.toJson()),
      );

      if (response.statusCode == 401) {
        throw Exception('Session expired. Please login again.');
      }

      if (response.statusCode != 200) {
        throw Exception('Failed to submit response: ${response.statusCode}');
      }

      return true;
    } catch (e) {
      throw Exception('Failed to submit response: ${e.toString()}');
    }
  }

  Future<bool> deleteFeedback(
    String token,
    String feedbackId,
  ) async {
    try {
      final response = await http.delete(
        Uri.parse('$feedback/$feedbackId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 401) {
        throw Exception('Session expired. Please login again.');
      }

      if (response.statusCode != 200) {
        throw Exception('Failed to delete feedback: ${response.statusCode}');
      }

      return true;
    } catch (e) {
      throw Exception('Failed to delete feedback: ${e.toString()}');
    }
  }

  /// Helper method to get the new token from SharedPreferences
  Future<String> _getNewToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token') ?? '';
  }

  // Helper method to safely show snackbars using UIHelper
  void _safeShowSnackBar(String message, {bool isError = true}) {
    uiHelper.showSnackBar(message, isError: isError);
  }
}
