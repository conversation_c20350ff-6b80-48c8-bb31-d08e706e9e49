// ignore_for_file: avoid_print, deprecated_member_use

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:geocoding/geocoding.dart';
import 'package:provider/provider.dart';
import '../../utils/snackbar_helper.dart';
import '../../controllers/location_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../data/models/error_response_model.dart';
import '../../utils/app_theme.dart';

class AttendancePage extends StatefulWidget {
  const AttendancePage({super.key});

  @override
  State<AttendancePage> createState() => _AttendancePageState();
}

class _AttendancePageState extends State<AttendancePage> {
  Position? currentPosition;
  bool isLoading = true;
  String currentAddress = "---";
  late final LocationController _locationController;
  // No longer needed as we use the built-in 'mounted' property
  bool _isLocationVerified = false;
  bool _isCheckedIn = false; // Track check-in state
  static const String _checkInStateKey =
      'isCheckedIn'; // Key for SharedPreferences

  @override
  void initState() {
    super.initState();
    // Initialize controllers
    _locationController = Provider.of<LocationController>(
      context,
      listen: false,
    );

    // Load saved state
    _loadCheckInState();

    // Initialize location
    _initializeLocation();

    // Set up a listener to check for changes in the check-in state
    _setupCheckInStateListener();
  }

  // Set up a listener to periodically check for changes in the check-in state
  void _setupCheckInStateListener() {
    // Cancel any existing timer
    _checkInStateTimer?.cancel();

    // Check every 30 seconds for changes in the check-in state
    _checkInStateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _loadCheckInState();
      } else {
        // Cancel the timer if the widget is no longer mounted
        timer.cancel();
      }
    });
  }

  // Load the saved check-in state
  Future<void> _loadCheckInState() async {
    final prefs = await SharedPreferences.getInstance();
    if (mounted) {
      setState(() {
        _isCheckedIn = prefs.getBool(_checkInStateKey) ?? false;
      });
    }
  }

  // Save the check-in state
  Future<void> _saveCheckInState(bool isCheckedIn) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_checkInStateKey, isCheckedIn);
  }

  // Timer for check-in state listener
  Timer? _checkInStateTimer;

  @override
  void dispose() {
    // Cancel the check-in state timer
    _checkInStateTimer?.cancel();
    // Call super.dispose() last
    super.dispose();
  }

  // Optimized location initialization with better error handling
  Future<void> _initializeLocation() async {
    // Use a single function to check and request location permissions
    // This reduces code duplication and improves maintainability
    try {
      // Check if location services are enabled - don't use isolate for Geolocator
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();

      if (!mounted) return;

      if (!serviceEnabled) {
        if (mounted) {
          // Use a safer approach to show error message
          _showErrorMessage(
            'Location services are disabled. Please enable location services.',
          );
          setState(() => isLoading = false);
        }
        return;
      }

      // Check and request location permissions
      final permissionGranted = await _checkAndRequestLocationPermission();
      if (!permissionGranted || !mounted) {
        return; // Permission handling is done in _checkAndRequestLocationPermission
      }

      // Get current position with timeout - don't use isolate for Geolocator
      try {
        final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
        ).timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            throw TimeoutException('Location request timed out');
          },
        );

        if (!mounted) return;

        setState(() {
          currentPosition = position;
        });

        // Get address and verify location in parallel for better performance
        await Future.wait([
          _getAddressFromLatLng(position),
          _verifyLocationAndUpdateState(position),
        ]);

        if (!mounted) return;
        setState(() => isLoading = false);
      } catch (e) {
        if (mounted) {
          _showErrorMessage('Error getting location: ${e.toString()}');
          setState(() => isLoading = false);
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage('Error initializing location: ${e.toString()}');
        setState(() => isLoading = false);
      }
    }
  }

  // Helper method to check and request location permission
  Future<bool> _checkAndRequestLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (!mounted) return false;

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (!mounted) return false;

        if (permission == LocationPermission.denied) {
          if (mounted) {
            _showErrorMessage('Location permission denied');
            setState(() => isLoading = false);
          }
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        if (mounted) {
          _showErrorMessage(
            'Location permissions are permanently denied. Please enable in settings.',
          );
          setState(() => isLoading = false);
        }
        return false;
      }

      return true;
    } catch (e) {
      if (mounted) {
        _showErrorMessage('Error requesting permission: ${e.toString()}');
        setState(() => isLoading = false);
      }
      return false;
    }
  }

  // Optimized address retrieval with better error handling
  Future<void> _getAddressFromLatLng(Position position) async {
    try {
      // Don't use isolate for geocoding to avoid background isolate issues
      final placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (!mounted) return;

      if (placemarks.isNotEmpty) {
        final place = placemarks[0];

        // Create address string only with available components
        final addressComponents = <String>[];
        if (place.street?.isNotEmpty ?? false) addressComponents.add(place.street!);
        if (place.subLocality?.isNotEmpty ?? false) addressComponents.add(place.subLocality!);
        if (place.locality?.isNotEmpty ?? false) addressComponents.add(place.locality!);
        if (place.postalCode?.isNotEmpty ?? false) addressComponents.add(place.postalCode!);

        final formattedAddress = addressComponents.join(', ');

        if (mounted) {
          setState(() {
            currentAddress = formattedAddress.isNotEmpty
                ? formattedAddress
                : "Address not available";
          });
        }
      }
    } catch (e) {
      // Don't update UI on error, just log it
      debugPrint('Error getting address: $e');
      // Use a fallback address instead of showing an error
      if (mounted) {
        setState(() {
          currentAddress = "Address information unavailable";
        });
      }
    }
  }

  // Optimized location refresh with better performance
  Future<void> _getCurrentLocation() async {
    if (!mounted) return;

    setState(() {
      isLoading = true;
      _isLocationVerified = false;
    });

    try {
      // Check if location permission is granted first
      final permissionGranted = await _checkAndRequestLocationPermission();
      if (!permissionGranted || !mounted) {
        setState(() => isLoading = false);
        return;
      }

      // Get position directly - don't use isolate for Geolocator
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      ).timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          throw TimeoutException('Location request timed out');
        },
      );

      if (!mounted) return;

      setState(() {
        currentPosition = position;
      });

      // Update position and get address in parallel for better performance
      await Future.wait([
        _getAddressFromLatLng(position),
        _verifyLocationAndUpdateState(position),
      ]);
    } catch (e) {
      if (!mounted) return;
      SnackBarHelper.showErrorSnackBar(
        context,
        'Error updating location: ${e.toString()}',
      );
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  // Helper method to safely show error messages
  void _showErrorMessage(String message) {
    if (mounted) {
      _showSnackBar(message, isError: true);
    }
  }

  // Safe method to show snackbars without BuildContext across async gaps
  void _showSnackBar(
    String message, {
    bool isError = false,
    SnackBarAction? action,
  }) {
    if (!mounted) return;

    // Store a local reference to the context
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Use synchronous approach to avoid async gap issues
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: isError ? Colors.red : Colors.blue,
            action: action,
          ),
        );
      }
    });
  }

  Future<void> _verifyLocationAndUpdateState(Position position) async {
    // Capture context before async gap
    final currentContext = context;

    try {
      if (!mounted) return;

      // Verify with server
      bool isVerified = await _locationController.verifyLocation(
        currentContext,
        position,
      );
      if (mounted) {
        setState(() {
          _isLocationVerified = isVerified;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLocationVerified = false;
        });
        rethrow;
      }
    }
  }

  Future<void> _openGoogleMaps() async {
    if (currentPosition == null) {
      if (mounted) {
        _showSnackBar('Location not available');
      }
      return;
    }

    final url =
        'https://www.google.com/maps/search/?api=1&query=${currentPosition!.latitude},${currentPosition!.longitude}';

    try {
      final canOpenUrl = await canLaunch(url);
      if (canOpenUrl) {
        await launch(url);
      } else if (mounted) {
        _showSnackBar('Could not open Google Maps');
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar('Error opening maps: ${e.toString()}', isError: true);
      }
    }
  }

  Future<void> _refreshLocation() async {
    try {
      await _getCurrentLocation();
    } catch (e) {
      if (!mounted) return;
      // Use our safe method instead of directly using context
      _showSnackBar('Failed to refresh location: $e', isError: true);
    }
  }

  bool _isCheckInTimeValid() {
    final now = DateTime.now();
    final checkInTime = DateTime(
      now.year,
      now.month,
      now.day,
      7,
      55,
    ); // 7:55 AM
    return now.isAfter(checkInTime);
  }

  Future<void> _handleCheckInOut() async {
    try {
      if (currentPosition == null) {
        throw Exception('Location not available');
      }

      final newCheckInState = !_isCheckedIn;

      // Check time restriction only for check-in
      if (newCheckInState && !_isCheckInTimeValid()) {
        throw ErrorResponseModel(
          status: 'error',
          message: 'Check-in is only available after 7:55 AM',
          code: 'TIME_ERROR',
        );
      }

      bool success = false;

      // Capture context before async operations
      final currentContext = context;

      // Perform check-in/out
      if (newCheckInState) {
        // Performing check-in
        success = await _locationController.performCheckIn(
          currentContext,
          currentPosition!,
        );
      } else {
        // Performing check-out
        success = await _locationController.performCheckOut(
          currentContext,
          currentPosition!,
        );
      }

      if (success) {
        setState(() {
          _isCheckedIn = newCheckInState;
        });

        // Save the new state
        await _saveCheckInState(newCheckInState);
      }
    } catch (e) {
      // If there's an error, don't change the state

      // Use a safer approach to show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(e is ErrorResponseModel ? e.message : e.toString()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Check-in/out Attendance'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      // Use RepaintBoundary to optimize rendering
      body: RepaintBoundary(
        child: Column(
          children: [
            // Status header
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              padding: const EdgeInsets.only(bottom: 24, left: 24, right: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Current status indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: _isCheckedIn
                          ? AppTheme.successColor.withValues(alpha: 0.2)
                          : Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _isCheckedIn ? Icons.check_circle : Icons.timelapse,
                          color: _isCheckedIn ? Colors.white : Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isCheckedIn ? 'Currently Checked In' : 'Not Checked In',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            Expanded(
              child: RefreshIndicator(
                onRefresh: _refreshLocation,
                color: AppTheme.primaryColor,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Container(
                    constraints: BoxConstraints(
                      minHeight:
                          MediaQuery.of(context).size.height -
                          (AppBar().preferredSize.height +
                              MediaQuery.of(context).padding.top +
                              150),
                    ),
                    padding: const EdgeInsets.all(24),
                    child: isLoading
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(height: 80),
                                const CircularProgressIndicator(),
                                const SizedBox(height: 24),
                                Text(
                                  'Getting your location...',
                                  style: TextStyle(
                                    color: AppTheme.textSecondaryColor,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : _buildLocationContent(context),
                  ),
                ),
              ),
            ),
            // Show check-in button if location is verified
            if (_isLocationVerified) _buildCheckInOutButton(context),
          ],
        ),
      ),
    );
  }

  // Extracted method for location content to improve readability and maintainability
  Widget _buildLocationContent(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Location icon with animation
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppTheme.primaryLightColor,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.location_on,
            size: 60,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'Your Current Location',
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Make sure you are at your designated workplace',
          style: TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondaryColor,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),

        // Location info card with modern design
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Address section
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryLightColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      Icons.home_work_outlined,
                      color: AppTheme.primaryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Address',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          currentAddress,
                          style: TextStyle(
                            fontSize: 16,
                            color: AppTheme.textPrimaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Divider(),
              ),

              // Coordinates section
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryLightColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      Icons.gps_fixed,
                      color: AppTheme.primaryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Coordinates',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Lat: ${currentPosition?.latitude.toStringAsFixed(6) ?? "---"}',
                          style: TextStyle(
                            fontSize: 15,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'Long: ${currentPosition?.longitude.toStringAsFixed(6) ?? "---"}',
                          style: TextStyle(
                            fontSize: 15,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // View on map button with improved styling
        ElevatedButton.icon(
          onPressed: _openGoogleMaps,
          icon: const Icon(Icons.map_outlined),
          label: const Text('View on Google Maps'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.secondaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),

        const SizedBox(height: 32),

        // Location verification status with improved design
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          decoration: BoxDecoration(
            color: _isLocationVerified
                ? AppTheme.successColor.withValues(alpha: 0.1)
                : AppTheme.errorColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _isLocationVerified
                  ? AppTheme.successColor.withValues(alpha: 0.3)
                  : AppTheme.errorColor.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _isLocationVerified
                      ? AppTheme.successColor.withValues(alpha: 0.2)
                      : AppTheme.errorColor.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _isLocationVerified ? Icons.check_circle : Icons.error_outline,
                  color: _isLocationVerified ? AppTheme.successColor : AppTheme.errorColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isLocationVerified ? 'Location Verified' : 'Location Not Verified',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _isLocationVerified ? AppTheme.successColor : AppTheme.errorColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _isLocationVerified
                          ? 'You can now check-in/out at this location.'
                          : 'Please make sure you are at your designated workplace.',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Extracted method for check-in/out button to improve readability
  Widget _buildCheckInOutButton(BuildContext context) {
    final isCheckIn = !_isCheckedIn;
    final buttonEnabled = (_isCheckedIn || _isCheckInTimeValid());

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Time restriction notice for check-in
          if (isCheckIn && !_isCheckInTimeValid())
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Text(
                'Check-in is only available after 7:55 AM',
                style: TextStyle(
                  color: AppTheme.warningColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),

          // Check-in/out button
          ElevatedButton(
            onPressed: buttonEnabled ? _handleCheckInOut : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: isCheckIn ? AppTheme.successColor : AppTheme.errorColor,
              foregroundColor: Colors.white,
              disabledBackgroundColor: Colors.grey[300],
              disabledForegroundColor: Colors.grey[600],
              minimumSize: const Size(double.infinity, 56),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  isCheckIn ? Icons.login : Icons.logout,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  isCheckIn ? 'Check-in Now' : 'Check-out Now',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
