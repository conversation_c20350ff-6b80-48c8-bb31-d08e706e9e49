// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'check_in_out_screen.dart';
import 'profile_screen.dart';
import 'login_screen.dart';
import 'view_attendance_history_screen.dart';
import 'feedback_list_screen.dart';
import 'package:apploqic_attendance/controllers/auth_controller.dart';
import 'package:apploqic_attendance/views/widgets/menu_button.dart';
import 'package:apploqic_attendance/utils/performance_utils.dart';
import 'package:apploqic_attendance/utils/silent_checkout_helper.dart';
import 'package:apploqic_attendance/utils/app_theme.dart';
import 'package:intl/intl.dart';
import 'leave_application_list_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // Define menu items once to avoid rebuilding
  static const List<Map<String, dynamic>> _menuItems = [
    {
      'icon': Icons.how_to_reg,
      'label': 'CHECK IN/OUT\nATTENDANCE',
      'route': AttendancePage(),
    },
    {
      'icon': Icons.event_available,
      'label': 'VIEW\nATTENDANCE',
      'route': ViewAttendance(),
    },
    {
      'icon': Icons.local_post_office,
      'label': 'FEEDBACK\nFORM',
      'route': ComplaintListPage(),
    },
    {
      'icon': Icons.event_available,
      'label': 'LEAVE\nAPPLICATION',
      'route': LeaveApplicationListScreen(),
    },
  ];

  @override
  void initState() {
    super.initState();
    // Schedule the evening checkout when the home screen is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeEveningCheckout();
    });
  }

  @override
  void dispose() {
    // Cancel the evening checkout timer when the home screen is disposed
    SilentCheckoutHelper.cancelEveningCheckout();
    super.dispose();
  }

  // Initialize the evening checkout scheduler
  void _initializeEveningCheckout() {
    final authController = Provider.of<AuthController>(context, listen: false);
    if (authController.token != null) {
      SilentCheckoutHelper.scheduleEveningCheckout(authController.token!);
    }
  }

  // Get the menu items
  Future<List<Map<String, dynamic>>> _getMenuItems(BuildContext context) async {
    return _menuItems;
  }

  // Handle navigation with route caching
  void _navigateTo(BuildContext context, Widget route) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => route));
  }

  // Handle logout process
  Future<void> _handleLogout(BuildContext context) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // Get the AuthController and perform logout
      final authController = context.read<AuthController>();

      // Cancel the evening checkout timer before logout
      SilentCheckoutHelper.cancelEveningCheckout();

      await authController.logout();

      // Close loading indicator and navigate to login screen
      if (context.mounted) {
        Navigator.pop(context);
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => LoginScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      // Handle any errors during logout
      if (context.mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Logout failed: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get current user info
    final authController = Provider.of<AuthController>(context);
    final user = authController.currentUser;
    final now = DateTime.now();
    final greeting = _getGreeting(now.hour);
    final dateFormat = DateFormat('EEEE, MMMM d, yyyy');
    final formattedDate = dateFormat.format(now);

    // Use RepaintBoundary to optimize rendering
    return RepaintBoundary(
      child: Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        appBar: AppBar(
          elevation: 0,
          actions: [
            PopupMenuButton<String>(
              onSelected: (value) async {
                switch (value) {
                  case 'profile':
                    _navigateTo(context, const ProfileScreen());
                    break;
                  case 'logout':
                    await _handleLogout(context);
                    break;
                }
              },
              itemBuilder:
                  (BuildContext context) => [
                    PopupMenuItem<String>(
                      value: 'profile',
                      child: Row(
                        children: [
                          Icon(Icons.person, color: AppTheme.primaryColor),
                          const SizedBox(width: 8),
                          const Text('Profile'),
                        ],
                      ),
                    ),
                    PopupMenuItem<String>(
                      value: 'logout',
                      child: Row(
                        children: [
                          Icon(Icons.logout, color: AppTheme.errorColor),
                          const SizedBox(width: 8),
                          const Text('Logout'),
                        ],
                      ),
                    ),
                  ],
            ),
          ],
          centerTitle: true,
          title: const Text(
            'Apploqic Attendance',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User welcome section with gradient background
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        AppTheme.primaryColor,
                        AppTheme.primaryColor.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(24),
                      bottomRight: Radius.circular(24),
                    ),
                  ),
                  padding: const EdgeInsets.fromLTRB(24, 16, 24, 30),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Greeting and user name
                      Text(
                        '$greeting,',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user?.name ?? 'User',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        formattedDate,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),

                // Menu section
                Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Text(
                        'Quick Actions',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      const SizedBox(height: 20),
                      // Use optimized grid view for better performance
                      FutureBuilder<List<Map<String, dynamic>>>(
                        future: _getMenuItems(context),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          }

                          final items = snapshot.data ?? _menuItems;

                          return PerformanceUtils.optimizedGridView(
                            items: items,
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  mainAxisSpacing: 20.0,
                                  crossAxisSpacing: 20.0,
                                  childAspectRatio: 1.1,
                                ),
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              final item = items[index];
                              return MenuButton(
                                icon: item['icon'],
                                label: item['label'],
                                onTap:
                                    () => _navigateTo(context, item['route']),
                              );
                            },
                          );
                        },
                      ),
                      const SizedBox(height: 30),
                      // Version info
                      Center(
                        child: Text(
                          'Version 1.0.0',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textLightColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to get appropriate greeting based on time of day
  String _getGreeting(int hour) {
    if (hour < 12) {
      return 'Good morning';
    } else if (hour < 17) {
      return 'Good afternoon';
    } else {
      return 'Good evening';
    }
  }
}
