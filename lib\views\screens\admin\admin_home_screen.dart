// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:pie_chart/pie_chart.dart';
import 'package:apploqic_attendance/views/widgets/menu_button.dart';
import 'package:provider/provider.dart';
import 'package:apploqic_attendance/controllers/auth_controller.dart';
import 'package:apploqic_attendance/controllers/attendance_performance_controller.dart';
import 'package:apploqic_attendance/views/screens/login_screen.dart';
import 'package:apploqic_attendance/views/screens/admin/feedback_list_screen.dart';
import 'package:apploqic_attendance/utils/app_theme.dart';
import 'package:intl/intl.dart';
import 'package:apploqic_attendance/views/screens/admin/employee_history_screen.dart';

class AttendanceAdmin extends StatefulWidget {
  const AttendanceAdmin({super.key});

  @override
  State<AttendanceAdmin> createState() => _AttendanceAdminState();
}

class _AttendanceAdminState extends State<AttendanceAdmin> {
  @override
  void initState() {
    super.initState();
    // Fetch attendance stats when the screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchAttendanceStats();
    });
  }

  Future<void> _fetchAttendanceStats() async {
    final authController = Provider.of<AuthController>(context, listen: false);
    final performanceController = Provider.of<AttendancePerformanceController>(
      context,
      listen: false,
    );

    if (authController.token != null) {
      await performanceController.fetchTodayAttendanceStats(
        authController.token!,
      );
    }
  }

  // Method to handle admin logout
  Future<void> _handleAdminLogout(BuildContext context) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (dialogContext) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // Get the AuthController and perform logout
      final authController = Provider.of<AuthController>(
        context,
        listen: false,
      );
      await authController.logout();

      // Close loading indicator and navigate to login screen
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop(); // Pop the dialog
      }
      // Use the root navigator to push and remove until to avoid context issues
      Navigator.of(context, rootNavigator: true).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => LoginScreen()),
        (route) => false,
      );
    } catch (e) {
      // Handle any errors during logout
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop(); // Pop the dialog
      }
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Logout failed: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final dateFormat = DateFormat('EEEE, MMMM d, yyyy');
    final formattedDate = dateFormat.format(now);

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        centerTitle: true,
        title: const Text(
          'Apploqic Attendance',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchAttendanceStats,
          ),
          PopupMenuButton<String>(
            onSelected: (value) async {
              if (value == 'logout') {
                await _handleAdminLogout(context);
              }
            },
            itemBuilder:
                (BuildContext context) => [
                  PopupMenuItem<String>(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout, color: AppTheme.errorColor),
                        const SizedBox(width: 8),
                        const Text('Logout'),
                      ],
                    ),
                  ),
                ],
            icon: const Icon(Icons.more_vert, color: Colors.white),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section with gradient background
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppTheme.primaryColor,
                      AppTheme.primaryColor.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(24),
                    bottomRight: Radius.circular(24),
                  ),
                ),
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 30),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Welcome,',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'Christopher Chai',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      formattedDate,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),

              // Attendance Analysis Card
              Padding(
                padding: const EdgeInsets.all(24.0),
                child: Consumer<AttendancePerformanceController>(
                  builder: (context, controller, child) {
                    if (controller.isLoading) {
                      return const Card(
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Center(child: CircularProgressIndicator()),
                        ),
                      );
                    }

                    if (controller.error != null) {
                      return Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              const Icon(
                                Icons.error_outline,
                                color: Colors.red,
                                size: 48,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Error loading attendance data',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                controller.error!,
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(color: Colors.red),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton.icon(
                                onPressed: _fetchAttendanceStats,
                                icon: const Icon(Icons.refresh),
                                label: const Text('Retry'),
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    if (controller.totalActiveUsers == 0) {
                      return Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              const Icon(
                                Icons.info_outline,
                                color: Colors.blue,
                                size: 48,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No attendance data available',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'There are no active users in the system',
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    final present = controller.presentCount;
                    final absent = controller.absentCount;
                    final total = controller.totalActiveUsers;
                    final attendanceRate =
                        total > 0 ? ((present / total) * 100).round() : 0;
                    final dataMap = {
                      "Present": present.toDouble(),
                      "Absent": absent.toDouble(),
                    };

                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const EmployeeHistoryScreen(),
                          ),
                        );
                      },
                      child: Card(
                        elevation: 4,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                          side: BorderSide(
                            color: Colors.grey.shade200,
                            width: 1,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              PieChart(
                                dataMap: dataMap,
                                chartType: ChartType.ring,
                                ringStrokeWidth: 28,
                                chartRadius:
                                    MediaQuery.of(context).size.width / 2.2,
                                colorList: const [
                                  AppTheme.primaryColor,
                                  AppTheme.errorColor,
                                ],
                                centerText: "$attendanceRate%",
                                centerTextStyle: const TextStyle(
                                  fontSize: 32,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.textPrimaryColor,
                                ),
                                legendOptions: const LegendOptions(
                                  showLegends: false,
                                ),
                                chartValuesOptions: const ChartValuesOptions(
                                  showChartValues: false,
                                  showChartValuesOutside: false,
                                  showChartValuesInPercentage: false,
                                ),
                                animationDuration: const Duration(
                                  milliseconds: 900,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  _buildLegendPill(
                                    AppTheme.primaryColor,
                                    'Present',
                                  ),
                                  const SizedBox(width: 18),
                                  _buildLegendPill(
                                    AppTheme.errorColor,
                                    'Absence',
                                  ),
                                ],
                              ),
                              const SizedBox(height: 18),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 12,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: Colors.grey.shade200,
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    _buildStatNumber(
                                      'Present',
                                      present,
                                      AppTheme.primaryColor,
                                    ),
                                    const SizedBox(width: 32),
                                    _buildStatNumber(
                                      'Absence',
                                      absent,
                                      AppTheme.errorColor,
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 18),
                              const Text(
                                'Today Attendance',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1.1,
                                  color: AppTheme.textPrimaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

              // Menu Items Grid
              Padding(
                padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Text(
                      'Quick Actions',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 20),
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      mainAxisSpacing: 16.0,
                      crossAxisSpacing: 16.0,
                      children: [
                        MenuButton(
                          icon: Icons.feedback,
                          label: 'FEEDBACK\nLIST',
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) =>
                                        const AdminFeedbackListScreen(),
                              ),
                            );
                          },
                        ),
                        MenuButton(
                          icon: Icons.update,
                          label: 'Coming Soon',
                          onTap: () {
                            // TODO: Add functionality
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Version info
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'Version 1.0.0',
                    style: TextStyle(
                      color: AppTheme.textLightColor,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLegendPill(Color color, String label) {
    return Row(
      children: [
        Container(
          width: 24,
          height: 14,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(7),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 15,
            color: AppTheme.textPrimaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildStatNumber(String label, int value, Color color) {
    return Column(
      children: [
        Text(
          value.toString(),
          style: TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w500,
            color: AppTheme.textPrimaryColor,
          ),
        ),
      ],
    );
  }
}
