import 'package:flutter/material.dart';
import 'dart:convert';
import '../data/services/auth_service.dart';
import '../data/services/token_service.dart';
import '../data/services/fcm_service.dart';
import '../data/models/login_response_model.dart';
import '../data/models/user_model.dart';
import '../utils/token_helper.dart';
import '../utils/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthController with ChangeNotifier {
  final AuthService _authService = AuthService();
  final TokenService _tokenService = TokenService();
  final FCMService _fcmService = FCMService();
  LoginResponseModel? _loginData;

  UserModel? get currentUser => _loginData?.user;
  String? get token => _loginData?.token;
  bool get isLoggedIn => _loginData?.token != null;

  // Initialize the controller and try to restore saved data
  Future<void> init() async {
    await _restoreLoginData();
  }

  Future<void> _restoreLoginData() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLoginData = prefs.getString('loginData');

    if (savedLoginData != null) {
      try {
        final Map<String, dynamic> jsonData = json.decode(savedLoginData);
        _loginData = LoginResponseModel.fromJson(jsonData);
        notifyListeners();
      } catch (e) {
        // If there's an error parsing the data, clear it
        await prefs.remove('loginData');
      }
    }
  }

  Future<void> login(String login, String password) async {
    _loginData = await _authService.login(login, password);

    if (_loginData != null) {
      // Save tokens
      await saveTokens(
        _loginData!.token,
        _loginData!.refreshToken,
        _loginData!.expiresIn,
      );

      // Save complete login data
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('loginData', json.encode(_loginData));

      // Make sure check-in state is reset for the new user
      await prefs.remove('isCheckedIn');
      await prefs.remove('current_attendance_uuid');

      // Register FCM token
      try {
        final fcmToken = await _fcmService.getToken();
        if (fcmToken != null) {
          await _fcmService.registerDevice(_loginData!.token, fcmToken);
        }
      } catch (e) {
        print('Error registering FCM token: $e');
        // Don't throw error here as FCM registration is not critical for login
      }
    }

    notifyListeners();
  }

  Future<void> logout() async {
    _loginData = null;

    final prefs = await SharedPreferences.getInstance();

    // Clear all stored data
    await prefs.clear();

    notifyListeners();
  }

  Future<bool> isAdmin() async {
    return _loginData?.user.role == 'admin';
  }

  Future<bool> refreshTokenIfNeeded() async {
    final isExpired = await isTokenExpired();
    if (!isExpired) return true;

    final prefs = await SharedPreferences.getInstance();
    final refreshToken = prefs.getString(refreshTokenKey);

    if (refreshToken == null) {
      await logout();
      return false;
    }

    try {
      final refreshResponse = await _tokenService.refreshToken(refreshToken);

      if (_loginData != null) {
        // Update login data with new token
        _loginData = LoginResponseModel(
          token: refreshResponse.token,
          refreshToken: refreshToken, // Keep existing refresh token
          expiresIn: refreshResponse.expiresIn,
          user: _loginData!.user,
        );

        // Save updated login data
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('loginData', json.encode(_loginData));
      }

      notifyListeners();
      return true;
    } catch (e) {
      await logout();
      return false;
    }
  }
}
