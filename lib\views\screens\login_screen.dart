// ignore_for_file: use_key_in_widget_constructors, library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:apploqic_attendance/controllers/auth_controller.dart';
import 'package:apploqic_attendance/views/screens/home_screen.dart';
import 'package:apploqic_attendance/utils/snackbar_helper.dart';
import 'package:apploqic_attendance/utils/app_theme.dart';
import 'package:provider/provider.dart';
import 'package:apploqic_attendance/views/screens/admin/admin_home_screen.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool _obscurePassword = true;
  bool _isLoading = false; // Add loading state
  final _loginController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _loginController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // Helper method to safely show snackbars
  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    // Use a post-frame callback to ensure UI updates happen safely
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        if (isError) {
          SnackBarHelper.showErrorSnackBar(context, message);
        } else {
          SnackBarHelper.showSuccessSnackBar(context, message);
        }
      }
    });
  }

  Future<void> _handleLogin(BuildContext context) async {
    // Validate inputs synchronously before any async operations
    if (_loginController.text.isEmpty || _passwordController.text.isEmpty) {
      _showSnackBar('Please enter both fields', isError: true);
      return;
    }

    // Capture context before async operations
    final currentContext = context;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get auth controller before async operations
      final authController = Provider.of<AuthController>(
        currentContext,
        listen: false,
      );

      // Store credentials in local variables
      final login = _loginController.text;
      final password = _passwordController.text;

      // Perform login
      await authController.login(login, password);

      // Check if widget is still mounted
      if (!mounted) return;

      // Handle successful login
      if (authController.isLoggedIn) {
        _showSnackBar('Login successful!');

        // Check if the user is an admin
        final isAdmin = await authController.isAdmin();

        // Navigate to the appropriate screen
        if (mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(
                  builder:
                      (context) =>
                          isAdmin
                              ? const AttendanceAdmin()
                              : const HomeScreen(),
                ),
                (route) => false,
              );
            }
          });
        }
      }
    } catch (e) {
      // Handle login error
      if (!mounted) return;
      _showSnackBar(e.toString(), isError: true);
    } finally {
      // Reset loading state
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryLightColor,
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Stack(
          children: [
            // Background design with curved shapes
            Positioned(
              top: -100,
              right: -50,
              child: Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
              ),
            ),
            Positioned(
              bottom: -80,
              left: -50,
              child: Container(
                width: 180,
                height: 180,
                decoration: BoxDecoration(
                  color: AppTheme.secondaryColor.withValues(alpha: 0.15),
                  shape: BoxShape.circle,
                ),
              ),
            ),
            // Main content
            SafeArea(
              child: SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight:
                        MediaQuery.of(context).size.height -
                        MediaQuery.of(context).padding.top -
                        MediaQuery.of(context).padding.bottom,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(height: 40),
                        // Logo with shadow
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(20),
                            child: Image.asset(
                              'assets/images/applogic_logo.png',
                              height: 180,
                              width: double.infinity,
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                        const SizedBox(height: 40),
                        // Welcome text with animation
                        TweenAnimationBuilder(
                          duration: const Duration(milliseconds: 600),
                          curve: Curves.easeOutCubic,
                          tween: Tween<double>(begin: 0, end: 1),
                          builder: (context, value, child) {
                            return Opacity(
                              opacity: value,
                              child: Transform.translate(
                                offset: Offset(0, 20 * (1 - value)),
                                child: Text(
                                  'Welcome Back',
                                  style: TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.primaryColor,
                                    letterSpacing: 0.5,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Sign in to continue',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                        const SizedBox(height: 40),
                        // Login form with card effect
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(24),
                          child: Column(
                            children: [
                              // Username field
                              TextField(
                                controller: _loginController,
                                enabled: !_isLoading,
                                decoration: AppTheme.inputDecoration(
                                  hintText: 'Email or Username',
                                  prefixIcon: Icon(
                                    Icons.person_outline,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 20),
                              // Password field
                              TextField(
                                controller: _passwordController,
                                enabled: !_isLoading,
                                obscureText: _obscurePassword,
                                decoration: AppTheme.inputDecoration(
                                  hintText: 'Password',
                                  prefixIcon: Icon(
                                    Icons.lock_outline,
                                    color: AppTheme.primaryColor,
                                  ),
                                  suffixIcon: IconButton(
                                    icon: Icon(
                                      _obscurePassword
                                          ? Icons.visibility_off
                                          : Icons.visibility,
                                      color: AppTheme.textSecondaryColor,
                                    ),
                                    onPressed:
                                        _isLoading
                                            ? null
                                            : () {
                                              setState(() {
                                                _obscurePassword =
                                                    !_obscurePassword;
                                              });
                                            },
                                  ),
                                ),
                              ),
                              const SizedBox(height: 30),
                              // Login button
                              SizedBox(
                                width: double.infinity,
                                height: 55,
                                child: ElevatedButton(
                                  onPressed:
                                      _isLoading
                                          ? null
                                          : () => _handleLogin(context),
                                  style: AppTheme.primaryButtonStyle,
                                  child:
                                      _isLoading
                                          ? const SizedBox(
                                            height: 24,
                                            width: 24,
                                            child: CircularProgressIndicator(
                                              color: Colors.white,
                                              strokeWidth: 2,
                                            ),
                                          )
                                          : const Text(
                                            'Login',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                            ),
                                          ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 30),
                        // Version info
                        Text(
                          'Apploqic Attendance v1.0.0',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textLightColor,
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
