class FeedbackRequest {
  final String feedbackType;
  final String subject;
  final String description;
  final String? images;

  FeedbackRequest({
    required this.feedbackType,
    required this.subject,
    required this.description,
    this.images,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'feedback_type': feedbackType,
      'subject': subject.trim(),
      'description': description.trim(),
    };

    if (images != null && images!.isNotEmpty) {
      data['images'] = images;
    }

    return data;
  }
}
