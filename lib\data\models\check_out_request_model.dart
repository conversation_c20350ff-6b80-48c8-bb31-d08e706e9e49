import 'check_in_request_model.dart';

class CheckOutRequest {
  final String? attendanceUuid;
  final int locationId;
  final Coordinates coordinates;
  final double accuracy;
  final DeviceInfo deviceInfo;
  final String timestamp;

  CheckOutRequest({
    this.attendanceUuid,
    required this.locationId,
    required this.coordinates,
    required this.accuracy,
    required this.deviceInfo,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'attendanceUuid': attendanceUuid ?? '',
    'locationId': locationId,
    'coordinates': coordinates.toJson(),
    'accuracy': accuracy,
    'deviceInfo': deviceInfo.toJson(),
    'timestamp': timestamp,
  };
}