class FeedbackList {
  final String id;
  final int userId;
  final String name;
  final String type;
  final String subject;
  final String description;
  final String? images;
  final String? response;
  final DateTime submittedAt;
  final DateTime lastUpdated;

  FeedbackList({
    required this.id,
    required this.userId,
    required this.name,
    required this.type,
    required this.subject,
    required this.description,
    this.images,
    this.response,
    required this.submittedAt,
    required this.lastUpdated,
  });

  factory FeedbackList.fromJson(Map<String, dynamic> json) {
    return FeedbackList(
      id: json['id'].toString(),
      userId: json['user_id'],
      name: json['name'] ?? '',
      type: json['type'],
      subject: json['subject'],
      description: json['description'],
      images: json['images'],
      response: json['response'],
      submittedAt: DateTime.parse(json['submitted_at']),
      lastUpdated: DateTime.parse(json['last_updated']),
    );
  }
}
