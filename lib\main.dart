import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:apploqic_attendance/views/screens/home_screen.dart';
import 'package:apploqic_attendance/views/screens/login_screen.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:apploqic_attendance/controllers/auth_controller.dart';
import 'package:apploqic_attendance/controllers/attendance_performance_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:apploqic_attendance/controllers/location_controller.dart';
import 'package:apploqic_attendance/utils/image_service.dart';
import 'package:apploqic_attendance/utils/network_service.dart';
import 'package:apploqic_attendance/utils/ui_helper.dart';
import 'package:apploqic_attendance/views/screens/admin/admin_home_screen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:apploqic_attendance/data/services/fcm_service.dart';

// Global services for easy access
final networkService = NetworkService();
final imageService = ImageService();
final fcmService = FCMService();

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Initialize Firebase messaging
  await fcmService.initializeMessaging();

  // Set preferred orientations for better performance
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Create controller instances
  final authController = AuthController();
  final locationController = LocationController();
  final attendancePerformanceController = AttendancePerformanceController();

  // Initialize auth controller
  await authController.init();

  // Run the app with error handling
  runApp(
    MyApp(
      authController: authController,
      locationController: locationController,
      attendancePerformanceController: attendancePerformanceController,
    ),
  );
}

class MyApp extends StatelessWidget {
  final AuthController authController;
  final LocationController locationController;
  final AttendancePerformanceController attendancePerformanceController;

  const MyApp({
    super.key,
    required this.authController,
    required this.locationController,
    required this.attendancePerformanceController,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: authController),
        ChangeNotifierProvider.value(value: locationController),
        ChangeNotifierProvider.value(value: attendancePerformanceController),
      ],
      child: GetMaterialApp(
        debugShowCheckedModeBanner: false,
        showPerformanceOverlay: false,
        title: 'Apploqic Attendance System',
        // Add navigator key for UIHelper to work
        navigatorKey: uiHelper.navigatorKey,
        theme: ThemeData(
          // Modern color scheme with a professional blue as primary color
          colorScheme: ColorScheme.light(
            primary: const Color(0xFF1565C0),
            primaryContainer: const Color(0xFFE3F2FD),
            secondary: const Color(0xFF00897B),
            secondaryContainer: const Color(0xFFE0F2F1),
            surface: Colors.white,
            surfaceContainerHighest: const Color(0xFFF5F5F7),
            error: const Color(0xFFD32F2F),
          ),
          useMaterial3: true,
          // Optimize text rendering
          textTheme: Typography.material2018().black.copyWith(
            // Headline styles
            headlineMedium: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1565C0),
            ),
            // Title styles
            titleLarge: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1E293B),
            ),
            // Body styles
            bodyLarge: const TextStyle(
              fontSize: 16,
              color: Color(0xFF334155),
            ),
            bodyMedium: const TextStyle(
              fontSize: 14,
              color: Color(0xFF475569),
            ),
          ),
          // Card theme
          cardTheme: CardTheme(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color: Colors.white,
          ),
          // Button theme
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              elevation: 0,
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          // Input decoration theme
          inputDecorationTheme: InputDecorationTheme(
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: const Color(0xFFE2E8F0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: const Color(0xFF1565C0), width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
          // Optimize scrolling physics
          platform: TargetPlatform.android,
          // Optimize page transitions
          pageTransitionsTheme: const PageTransitionsTheme(
            builders: {
              TargetPlatform.android: ZoomPageTransitionsBuilder(),
              TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
            },
          ),
        ),
        // Optimize app startup by showing splash screen while checking auth
        home: const AuthWrapper(),
      ),
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  // Cache the future to prevent recreating it on every build
  static final Future<bool> _tokenCheckFuture = _checkRefreshToken();

  static Future<bool> _checkRefreshToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('refreshToken') != null;
    } catch (e) {
      debugPrint('Error checking refresh token: $e');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: _tokenCheckFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          // Show a branded splash screen instead of just a spinner
          return Material(
            child: Container(
              color: Colors.white,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Use Image.asset instead of directly loading from network
                  Image.asset(
                    'assets/images/applogic_logo.png',
                    height: 120,
                    width: 120,
                  ),
                  const SizedBox(height: 24),
                  const CircularProgressIndicator(), ],
              ),
            ),
          );
        }

        // Token check is done
        if (snapshot.hasData && snapshot.data == true) {
          // Token exists, now check role from AuthController
          // AuthController.init() has already run in main(), so user data should be loaded
          final authController = Provider.of<AuthController>(
            context,
            listen: false,
          );
          if (authController.currentUser?.role == 'admin') {
            return const AttendanceAdmin(); // Navigate to Admin Home Screen
          } else {
            return const HomeScreen(); // Navigate to User Home Screen
          }
        } else {
          // No token or error in token check, navigate to LoginScreen
          return LoginScreen();
        }
      },
    );
  }
}
