class LeaveApplicationRequest {
  final String leaveType;
  final String startDate;
  final String endDate;
  final String reason;

  LeaveApplicationRequest({
    required this.leaveType,
    required this.startDate,
    required this.endDate,
    required this.reason,
  });

  Map<String, dynamic> toJson() {
    return {
      'leave_type': leaveType,
      'start_date': startDate,
      'end_date': endDate,
      'reason': reason,
    };
  }
}