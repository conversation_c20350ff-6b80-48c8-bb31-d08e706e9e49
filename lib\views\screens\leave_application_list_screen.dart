import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/leave_application_card.dart';
import '../../data/models/leave_application_list_model.dart';
import '../../data/services/leave_application_service.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/snackbar_helper.dart';
import '../../utils/app_theme.dart';

class LeaveApplicationListScreen extends StatefulWidget {
  const LeaveApplicationListScreen({super.key});

  @override
  State<LeaveApplicationListScreen> createState() =>
      _LeaveApplicationListScreenState();
}

class _LeaveApplicationListScreenState extends State<LeaveApplicationListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final LeaveApplicationService _leaveApplicationService =
      LeaveApplicationService();

  List<LeaveApplicationList> _leaveApplications = [];
  bool _isLoading = true;

  // Leave balance data
  static const int _totalAnnualLeaveDays = 14; // total annual days per year
  int _annualLeaveBalance = 14; // remaining days
  int _medicalLeaveTaken = 0; // days taken this year

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadLeaveApplications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadLeaveApplications() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final authController = Provider.of<AuthController>(
        context,
        listen: false,
      );
      final token = authController.token;

      if (token == null) {
        throw Exception('Not authenticated');
      }

      final leaveApplications = await _leaveApplicationService
          .getLeaveApplicationList(token, () async {
            final refreshSuccess = await authController.refreshTokenIfNeeded();
            if (!refreshSuccess) {
              throw Exception('Session expired. Please login again.');
            }
            return true;
          });

      if (mounted) {
        setState(() {
          _leaveApplications = leaveApplications;
          _calculateLeaveBalances();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        SnackBarHelper.showErrorSnackBar(context, e.toString());
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _calculateLeaveBalances() {
    // Calculate annual leave taken (only approved leaves)
    final annualLeaveTaken = _leaveApplications
        .where(
          (leave) =>
              leave.leaveType.toLowerCase().contains('annual') &&
              leave.status?.toLowerCase() == 'approved',
        )
        .fold(0, (sum, leave) => sum + leave.leaveDays);

    // Calculate medical leave taken (only approved leaves)
    final medicalLeaveTaken = _leaveApplications
        .where(
          (leave) =>
              leave.leaveType.toLowerCase().contains('medical') &&
              leave.status?.toLowerCase() == 'approved',
        )
        .fold(0, (sum, leave) => sum + leave.leaveDays);

    _annualLeaveBalance = _totalAnnualLeaveDays - annualLeaveTaken;
    _medicalLeaveTaken = medicalLeaveTaken;
  }

  List<LeaveApplicationList> get _filteredAnnualLeave {
    return _leaveApplications
        .where((leave) => leave.leaveType.toLowerCase().contains('annual'))
        .toList();
  }

  List<LeaveApplicationList> get _filteredMedicalLeave {
    return _leaveApplications
        .where((leave) => leave.leaveType.toLowerCase().contains('medical'))
        .toList();
  }

  Widget _buildStatsCard() {
    return AnimatedBuilder(
      animation: _tabController,
      builder: (context, child) {
        final isAnnualTab = _tabController.index == 0;
        return Container(
          margin: const EdgeInsets.all(24.0),
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors:
                  isAnnualTab
                      ? [
                        AppTheme.primaryColor,
                        AppTheme.primaryColor.withOpacity(0.8),
                      ]
                      : [
                        AppTheme.errorColor,
                        AppTheme.errorColor.withOpacity(0.8),
                      ],
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: (isAnnualTab
                        ? AppTheme.primaryColor
                        : AppTheme.errorColor)
                    .withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  isAnnualTab ? Icons.beach_access : Icons.local_hospital,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isAnnualTab
                          ? 'Annual Leave Balance'
                          : 'Medical Leave Taken',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          isAnnualTab
                              ? '$_annualLeaveBalance'
                              : '$_medicalLeaveTaken',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          isAnnualTab ? 'days left' : 'days used',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      isAnnualTab
                          ? 'Out of $_totalAnnualLeaveDays annual days'
                          : 'This year so far',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLeaveList(
    List<LeaveApplicationList> leaves,
    String emptyMessage,
  ) {
    if (leaves.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_note, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      itemCount: leaves.length,
      itemBuilder: (context, index) {
        final leave = leaves[index];
        return LeaveApplicationCard(
          leaveApplication: leave,
          onTap: () {
            // Handle tap - navigate to details or expand
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Tapped on ${leave.leaveType} leave application'),
                backgroundColor: AppTheme.primaryColor,
              ),
            );
          },

          onDelete: () {
            // Handle delete
            setState(() {
              _leaveApplications.removeWhere((l) => l.id == leave.id);
              _calculateLeaveBalances();
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Deleted ${leave.leaveType} leave application'),
                backgroundColor: AppTheme.errorColor,
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        title: const Text(
          'Leave Applications',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // Navigate to add new leave application
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Navigate to new leave application form'),
                  backgroundColor: AppTheme.primaryColor,
                ),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.beach_access), text: 'Annual Leave'),
            Tab(icon: Icon(Icons.local_hospital), text: 'Medical Leave'),
          ],
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
        ),
      ),
      body: Column(
        children: [
          _buildStatsCard(),
          const SizedBox(height: 16),
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : RefreshIndicator(
                      onRefresh: _loadLeaveApplications,
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          _buildLeaveList(
                            _filteredAnnualLeave,
                            'No annual leave applications found',
                          ),
                          _buildLeaveList(
                            _filteredMedicalLeave,
                            'No medical leave applications found',
                          ),
                        ],
                      ),
                    ),
          ),
        ],
      ),
    );
  }
}
