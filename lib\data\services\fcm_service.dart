import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:http/http.dart' as http;
import '/utils/constants.dart';

class FCMService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  Future<String?> getToken() async {
    try {
      // Request permission for notifications
      NotificationSettings settings = await _firebaseMessaging
          .requestPermission(alert: true, badge: true, sound: true);

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        try {
          // Get FCM token
          String? token = await _firebaseMessaging.getToken();
          return token;
        } catch (tokenError) {
          // Return null but don't crash the app
          return null;
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<bool> registerDevice(String token, String fcmToken) async {
    try {
      final response = await http.post(
        Uri.parse(registerDeviceToken),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'fcmToken': fcmToken,
          'platform': Platform.isAndroid ? 'Android' : 'iOS',
        }),
      );

      if (response.statusCode == 200) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Initialize Firebase messaging handlers
  Future<void> initializeMessaging() async {
    try {
      // Handle messages when app is in foreground
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        if (message.notification != null) {
          // Here you would show a local notification
        }
      });

      // Handle when user taps on notification and app was in background
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        // Here you would navigate to relevant screen based on message data
      });

      // Check if app was opened from a notification when app was terminated
      RemoteMessage? initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        // Here you would navigate to relevant screen based on message data
      }
    } catch (e) {
      print('Error initializing Firebase messaging: $e');
    }
  }
}
