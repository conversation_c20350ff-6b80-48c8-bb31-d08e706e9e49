import 'dart:io';

void main() async {
  // Get all Dart files in the lib directory
  final libDir = Directory('lib');
  final dartFiles = await _findDartFiles(libDir);
  
  print('Found ${dartFiles.length} Dart files to process');
  
  // Process each file
  for (final file in dartFiles) {
    await _updateImports(file);
  }
  
  print('Import update complete!');
}

Future<List<File>> _findDartFiles(Directory directory) async {
  final List<File> dartFiles = [];
  
  await for (final entity in directory.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      dartFiles.add(entity);
    }
  }
  
  return dartFiles;
}

Future<void> _updateImports(File file) async {
  try {
    String content = await file.readAsString();
    
    // Replace import statements
    final updatedContent = content.replaceAll(
      'package:flutter_application_2/',
      'package:apploqic_attendance/'
    );
    
    // Only write if changes were made
    if (content != updatedContent) {
      await file.writeAsString(updatedContent);
      print('Updated imports in ${file.path}');
    }
  } catch (e) {
    print('Error processing ${file.path}: $e');
  }
}
