class AttendanceHistory {
  final String attendanceUuid;
  final String userId;
  final String name;
  final String department;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;
  final String? duration;
  final LocationInfo checkInLocation;
  final LocationInfo? checkOutLocation;
  final String verificationStatus;
  final bool locationExemption;
  final String? remarks;

  AttendanceHistory({
    required this.attendanceUuid,
    required this.userId,
    required this.name,
    required this.department,
    this.checkInTime,
    this.checkOutTime,
    this.duration,
    required this.checkInLocation,
    this.checkOutLocation,
    required this.verificationStatus,
    required this.locationExemption,
    this.remarks,
  });

  factory AttendanceHistory.fromJson(Map<String, dynamic> json) {
    return AttendanceHistory(
      attendanceUuid: json['attendance_uuid'] ?? '',
      userId: json['user_id'] ?? '',
      name: json['name'] ?? '',
      department: json['department'] ?? '',
      checkInTime:
          json['check_in_time'] != null
              ? DateTime.parse(json['check_in_time'])
              : null,
      checkOutTime:
          json['check_out_time'] != null
              ? DateTime.parse(json['check_out_time'])
              : null,
      duration: json['duration'],
      checkInLocation: LocationInfo.fromJson(json['check_in_location'] ?? {}),
      checkOutLocation:
          json['check_out_location'] != null
              ? LocationInfo.fromJson(json['check_out_location'])
              : null,
      verificationStatus: json['verification_status'] ?? 'pending',
      locationExemption: json['location_exemption'] ?? true,
      remarks: json['remarks'],
    );
  }
}

class LocationInfo {
  final String coordinates;
  final String? accuracy;
  final bool verified;
  final DeviceInfo? deviceInfo;

  LocationInfo({
    required this.coordinates,
    this.accuracy,
    required this.verified,
    this.deviceInfo,
  });

  factory LocationInfo.fromJson(Map<String, dynamic> json) {
    return LocationInfo(
      coordinates: json['coordinates'] ?? '',
      accuracy: json['accuracy']?.toString(),
      verified: json['verified'] ?? false,
      deviceInfo:
          json['device_info'] != null
              ? DeviceInfo.fromJson(json['device_info'])
              : null,
    );
  }
}

class DeviceInfo {
  final String model;
  final String platform;
  final String version;

  DeviceInfo({
    required this.model,
    required this.platform,
    required this.version,
  });

  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      model: json['model'] ?? '',
      platform: json['platform'] ?? '',
      version: json['version'] ?? '',
    );
  }
}

class AttendanceApiResponse {
  final bool isAdmin;
  final List<AttendanceHistory> list;
  final PaginationInfo pagination;

  AttendanceApiResponse({
    required this.isAdmin,
    required this.list,
    required this.pagination,
  });

  factory AttendanceApiResponse.fromJson(Map<String, dynamic> json) {
    var historyList = json['list'] as List;
    List<AttendanceHistory> attendanceHistoryList =
        historyList.map((i) => AttendanceHistory.fromJson(i)).toList();

    return AttendanceApiResponse(
      isAdmin: json['is_admin'] ?? false,
      list: attendanceHistoryList,
      pagination: PaginationInfo.fromJson(json['pagination'] ?? {}),
    );
  }
}

class PaginationInfo {
  final int totalItems;
  final int totalPages;
  final int currentPage;
  final int perPage;

  PaginationInfo({
    required this.totalItems,
    required this.totalPages,
    required this.currentPage,
    required this.perPage,
  });

  factory PaginationInfo.fromJson(Map<String, dynamic> json) {
    return PaginationInfo(
      totalItems: json['total_items'] ?? 0,
      totalPages: json['total_pages'] ?? 0,
      currentPage: json['current_page'] ?? 0,
      perPage: json['per_page'] ?? 0,
    );
  }
}
