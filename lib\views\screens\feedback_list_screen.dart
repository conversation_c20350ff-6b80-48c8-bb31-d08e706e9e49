import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'feedback_form_screen.dart';
import 'update_feedback_screen.dart';
import '../../data/services/feedback_service.dart';
import '../../data/models/feedback_list_model.dart';
import '../widgets/feedback_card.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/snackbar_helper.dart';
import '../../utils/app_theme.dart';

class ComplaintListPage extends StatefulWidget {
  const ComplaintListPage({super.key});

  @override
  State<ComplaintListPage> createState() => _ComplaintListPageState();
}

class _ComplaintListPageState extends State<ComplaintListPage> {
  final FeedbackService _feedbackService = FeedbackService();
  List<FeedbackList> _feedbackList = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadFeedbackList();
  }

  Future<void> _loadFeedbackList() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final authController = Provider.of<AuthController>(
        context,
        listen: false,
      );
      final token = authController.token;

      if (token == null) {
        throw Exception('Not authenticated');
      }

      final feedbackList = await _feedbackService.getFeedbackList(
        token,
        () async {
          final refreshSuccess = await authController.refreshTokenIfNeeded();
          if (!refreshSuccess) {
            throw Exception('Session expired. Please login again.');
          }
          return true;
        },
      );

      if (mounted) {
        setState(() {
          _feedbackList = feedbackList;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        SnackBarHelper.showErrorSnackBar(context, e.toString());
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  List<FeedbackList> get _filteredFeedbackList {
    if (_searchQuery.isEmpty) {
      return _feedbackList;
    }
    return _feedbackList.where((feedback) {
      return feedback.subject.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          ) ||
          feedback.description.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        title: const Text(
          'Feedback List',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const Icon(Icons.search, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      decoration: const InputDecoration(
                        hintText: 'Search Feedback...',
                        border: InputBorder.none,
                        hintStyle: TextStyle(color: Colors.grey),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : RefreshIndicator(
                      onRefresh: _loadFeedbackList,
                      child:
                          _filteredFeedbackList.isEmpty
                              ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.feedback_outlined,
                                      size: 64,
                                      color: Colors.grey[400],
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'No feedback found',
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.grey[600],
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                              : ListView.builder(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24.0,
                                ),
                                itemCount: _filteredFeedbackList.length,
                                itemBuilder: (context, index) {
                                  final feedback = _filteredFeedbackList[index];
                                  return FeedbackCard(
                                    feedback: feedback,
                                    onEdit: () async {
                                      final result = await Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder:
                                              (context) => UpdateFeedbackScreen(
                                                feedback: feedback,
                                              ),
                                        ),
                                      );
                                      if (result == true) {
                                        _loadFeedbackList();
                                      }
                                    },
                                    onDelete: () async {
                                      try {
                                        final authController =
                                            Provider.of<AuthController>(
                                              context,
                                              listen: false,
                                            );
                                        final token = authController.token;

                                        if (token == null) {
                                          throw Exception('Not authenticated');
                                        }

                                        final success = await _feedbackService
                                            .deleteFeedback(token, feedback.id);

                                        if (success && mounted) {
                                          _loadFeedbackList();
                                          SnackBarHelper.showSuccessSnackBar(
                                            context,
                                            'Feedback deleted successfully',
                                          );
                                        }
                                      } catch (e) {
                                        if (mounted) {
                                          SnackBarHelper.showErrorSnackBar(
                                            context,
                                            e.toString(),
                                          );
                                        }
                                      }
                                    },
                                  );
                                },
                              ),
                    ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const ComplaintFormPage()),
          ).then((_) => _loadFeedbackList());
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}
