import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/attendance_card.dart';
import '../../data/models/attendance_filter_model.dart';
import '../../data/models/attendance_history_model.dart';
import '../../data/services/attendance_service.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/snackbar_helper.dart';
import '../../utils/app_theme.dart';

class ViewAttendance extends StatefulWidget {
  const ViewAttendance({super.key});

  @override
  State<ViewAttendance> createState() => _ViewAttendanceState();
}

class _ViewAttendanceState extends State<ViewAttendance> {
  final AttendanceFilter _filter = AttendanceFilter();
  final AttendanceService _attendanceService = AttendanceService();
  DateTime? _startDate;
  DateTime? _endDate;
  List<AttendanceHistory> attendanceList = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _fetchAttendanceHistory();
  }

  Future<void> _fetchAttendanceHistory() async {
    setState(() => isLoading = true);

    try {
      final authController = Provider.of<AuthController>(
        context,
        listen: false,
      );
      final token = authController.token;

      if (token == null) {
        throw Exception('Not authenticated');
      }

      final attendanceHistory = await _attendanceService.getAttendanceHistory(
        context,
        _filter,
      );

      if (mounted) {
        setState(() {
          attendanceList = attendanceHistory;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        SnackBarHelper.showErrorSnackBar(context, e.toString());
        setState(() => isLoading = false);
      }
    }
  }

  Future<void> _refreshAttendanceHistory() async {
    try {
      final authController = Provider.of<AuthController>(
        context,
        listen: false,
      );
      final token = authController.token;

      if (token == null) {
        throw Exception('Not authenticated');
      }

      final attendanceHistory = await _attendanceService.getAttendanceHistory(
        context,
        _filter,
      );

      if (mounted) {
        setState(() {
          attendanceList = attendanceHistory;
        });
      }
    } catch (e) {
      if (mounted) {
        SnackBarHelper.showErrorSnackBar(context, e.toString());
      }
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime now = DateTime.now();
    final DateTime lastDate = DateTime(now.year + 1, 12, 31);
    final DateTime firstDate = DateTime(2020, 1, 1);

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? (_startDate ?? now) : (_endDate ?? now),
      firstDate: firstDate,
      lastDate: lastDate,
    );

    if (picked != null && mounted) {
      final formattedDate =
          "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";

      setState(() {
        if (isStartDate) {
          _startDate = picked;
          _filter.dateFrom = formattedDate;
        } else {
          _endDate = picked;
          _filter.dateTo = formattedDate;
        }
      });
    }
  }

  void _applyFilter() {
    _fetchAttendanceHistory();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'View Attendance List',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: RefreshIndicator(
        onRefresh: _refreshAttendanceHistory,
        child: SingleChildScrollView(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    // Filter Card
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Filter',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.textPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    readOnly: true,
                                    onTap: () => _selectDate(context, true),
                                    decoration: InputDecoration(
                                      labelText: 'Start Date',
                                      suffixIcon: const Icon(
                                        Icons.calendar_today,
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    controller: TextEditingController(
                                      text: _filter.dateFrom ?? '',
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TextFormField(
                                    readOnly: true,
                                    onTap: () => _selectDate(context, false),
                                    decoration: InputDecoration(
                                      labelText: 'End Date',
                                      suffixIcon: const Icon(
                                        Icons.calendar_today,
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    controller: TextEditingController(
                                      text: _filter.dateTo ?? '',
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: _applyFilter,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.primaryColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: const Text('Apply Filter'),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Attendance List
                    isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : attendanceList.isEmpty
                        ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.history,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No attendance history',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        )
                        : ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: attendanceList.length,
                          itemBuilder: (context, index) {
                            return AttendanceCard(
                              attendance: attendanceList[index],
                            );
                          },
                        ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
