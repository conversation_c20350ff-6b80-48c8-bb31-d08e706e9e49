# Flutter Proguard Rules

# Keep Flutter app
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Keep Firebase
-keep class com.google.firebase.** { *; }

# Keep Geolocator plugin
-keep class com.baseflow.geolocator.** { *; }

# Keep Google Maps
-keep class com.google.android.gms.maps.** { *; }
-keep class com.google.android.gms.location.** { *; }

# Keep Dio
-keep class io.flutter.plugins.dio.** { *; }

# Keep URL Launcher
-keep class io.flutter.plugins.urllauncher.** { *; }

# Keep Device Info Plus
-keep class dev.fluttercommunity.plus.device_info.** { *; }

# Keep Shared Preferences
-keep class io.flutter.plugins.sharedpreferences.** { *; }

# Keep Connectivity Plus
-keep class dev.fluttercommunity.plus.connectivity.** { *; }

# Keep Cached Network Image
-keep class com.github.chuckerteam.chucker.** { *; }

# Keep our app package
-keep class com.apploqic.attendance.** { *; }
