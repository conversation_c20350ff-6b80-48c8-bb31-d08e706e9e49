import 'package:flutter/material.dart';
import '../../data/models/leave_application_list_model.dart';
import 'package:intl/intl.dart';
import '../../utils/app_theme.dart';

class LeaveApplicationCard extends StatelessWidget {
  final LeaveApplicationList leaveApplication;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const LeaveApplicationCard({
    super.key,
    required this.leaveApplication,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: _getLeaveTypeColor(
                        leaveApplication.leaveType,
                      ).withOpacity(0.1),
                      radius: 20,
                      child: Icon(
                        _getLeaveTypeIcon(leaveApplication.leaveType),
                        color: _getLeaveTypeColor(leaveApplication.leaveType),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            leaveApplication.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getLeaveTypeColor(
                                leaveApplication.leaveType,
                              ).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: _getLeaveTypeColor(
                                  leaveApplication.leaveType,
                                ).withOpacity(0.3),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _getLeaveTypeIcon(leaveApplication.leaveType),
                                  size: 14,
                                  color: _getLeaveTypeColor(
                                    leaveApplication.leaveType,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  leaveApplication.leaveType,
                                  style: TextStyle(
                                    color: _getLeaveTypeColor(
                                      leaveApplication.leaveType,
                                    ),
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (leaveApplication.status != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(
                            leaveApplication.status!,
                          ).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: _getStatusColor(
                              leaveApplication.status!,
                            ).withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          leaveApplication.status!.toUpperCase(),
                          style: TextStyle(
                            color: _getStatusColor(leaveApplication.status!),
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.calendar_today,
                            size: 16,
                            color: AppTheme.textSecondaryColor,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Duration',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.textSecondaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            '${leaveApplication.leaveDays} ${leaveApplication.leaveDays == 1 ? 'day' : 'days'}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppTheme.textPrimaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.date_range,
                            size: 16,
                            color: AppTheme.textSecondaryColor,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Period',
                            style: TextStyle(
                              fontSize: 12,
                              color: AppTheme.textSecondaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            '${_formatDate(leaveApplication.leaveFrom)} - ${_formatDate(leaveApplication.leaveTo)}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppTheme.textPrimaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Reason:',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  leaveApplication.leaveReason,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppTheme.textPrimaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (leaveApplication.createdAt != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Applied on ${DateFormat('MMM dd, yyyy').format(leaveApplication.createdAt!)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.textLightColor,
                    ),
                  ),
                ],
                if (onDelete != null) ...[
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.delete,
                            color: Colors.red,
                            size: 20,
                          ),
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder:
                                  (context) => AlertDialog(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    title: Row(
                                      children: [
                                        Icon(
                                          Icons.warning,
                                          color: Colors.red[400],
                                          size: 24,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text('Delete Application'),
                                      ],
                                    ),
                                    content: const Text(
                                      'Are you sure you want to delete this leave application? This action cannot be undone.',
                                    ),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Navigator.pop(context),
                                        child: const Text(
                                          'CANCEL',
                                          style: TextStyle(
                                            color: Colors.grey,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      Container(
                                        decoration: BoxDecoration(
                                          color: Colors.red.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: TextButton(
                                          onPressed: () {
                                            Navigator.pop(context);
                                            onDelete?.call();
                                          },
                                          child: const Text(
                                            'DELETE',
                                            style: TextStyle(
                                              color: Colors.red,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                            );
                          },
                          tooltip: 'Delete Application',
                          style: IconButton.styleFrom(
                            padding: const EdgeInsets.all(8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getLeaveTypeColor(String leaveType) {
    switch (leaveType.toLowerCase()) {
      case 'annual':
      case 'annual leave':
        return AppTheme.primaryColor;
      case 'medical':
      case 'medical leave':
      case 'sick':
      case 'sick leave':
        return AppTheme.errorColor;
      case 'emergency':
      case 'emergency leave':
        return AppTheme.warningColor;
      case 'maternity':
      case 'maternity leave':
        return Colors.pink;
      case 'paternity':
      case 'paternity leave':
        return Colors.blue;
      default:
        return AppTheme.secondaryColor;
    }
  }

  IconData _getLeaveTypeIcon(String leaveType) {
    switch (leaveType.toLowerCase()) {
      case 'annual':
      case 'annual leave':
        return Icons.beach_access;
      case 'medical':
      case 'medical leave':
      case 'sick':
      case 'sick leave':
        return Icons.local_hospital;
      case 'emergency':
      case 'emergency leave':
        return Icons.emergency;
      case 'maternity':
      case 'maternity leave':
        return Icons.pregnant_woman;
      case 'paternity':
      case 'paternity leave':
        return Icons.family_restroom;
      default:
        return Icons.event_note;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppTheme.warningColor;
      case 'approved':
        return AppTheme.successColor;
      case 'rejected':
        return AppTheme.errorColor;
      case 'cancelled':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('MMM dd').format(date);
    } catch (e) {
      return dateString;
    }
  }
}
