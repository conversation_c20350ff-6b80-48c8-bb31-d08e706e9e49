import 'package:flutter/material.dart';
import '../../utils/app_theme.dart';

class ProfileInfoSection extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final IconData? icon;

  const ProfileInfoSection({
    super.key,
    required this.title,
    required this.children,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with icon
        Row(
          children: [
            if (icon != null) ...[
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.primaryLightColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
            ],
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Divider for visual separation
        Container(
          height: 1,
          color: AppTheme.dividerColor,
          margin: const EdgeInsets.only(bottom: 16),
        ),
        // Section content
        ...children,
      ],
    );
  }
}