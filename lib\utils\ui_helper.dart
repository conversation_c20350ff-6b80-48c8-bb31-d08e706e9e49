import 'package:flutter/material.dart';
import 'package:apploqic_attendance/utils/snackbar_helper.dart';

/// A helper class to safely handle UI operations that require BuildContext
/// This avoids the "Don't use BuildContext across async gaps" warning
class UIHelper {
  /// Singleton instance
  static final UIHelper _instance = UIHelper._internal();

  /// Factory constructor to return the singleton instance
  factory UIHelper() {
    return _instance;
  }

  /// Private constructor
  UIHelper._internal();

  /// Global key for accessing the navigator and scaffold messenger
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Get the current BuildContext safely
  BuildContext? get currentContext => navigatorKey.currentContext;

  /// Show a snackbar safely without BuildContext across async gaps
  void showSnackBar(String message, {bool isError = false}) {
    final context = currentContext;
    if (context != null) {
      if (isError) {
        SnackBarHelper.showErrorSnackBar(context, message);
      } else {
        SnackBarHelper.showSuccessSnackBar(context, message);
      }
    }
  }

  /// Navigate to a new screen safely
  Future<T?> navigateTo<T>(Widget screen) async {
    final context = currentContext;
    if (context != null) {
      return Navigator.push<T>(
        context,
        MaterialPageRoute(builder: (context) => screen),
      );
    }
    return null;
  }

  /// Replace the current screen safely
  void replaceScreen(Widget screen) {
    final context = currentContext;
    if (context != null) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => screen),
      );
    }
  }

  /// Clear all screens and show a new screen
  void clearStackAndShow(Widget screen) {
    final context = currentContext;
    if (context != null) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => screen),
        (route) => false,
      );
    }
  }
}

/// Global instance for easy access
final uiHelper = UIHelper();
