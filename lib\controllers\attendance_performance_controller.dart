import 'package:flutter/material.dart';
import '../data/models/attendance_filter_model.dart';
import '../data/services/attendance_service_v2.dart';

class AttendancePerformanceController extends ChangeNotifier {
  final AttendanceServiceV2 _attendanceService = AttendanceServiceV2();
  bool _isLoading = false;
  int _presentCount = 0;
  int _absentCount = 0;
  int _totalActiveUsers = 0;
  String? _error;

  bool get isLoading => _isLoading;
  int get presentCount => _presentCount;
  int get absentCount => _absentCount;
  int get totalActiveUsers => _totalActiveUsers;
  String? get error => _error;

  Future<void> fetchTodayAttendanceStats(String token) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Get today's date in YYYY-MM-DD format
      final now = DateTime.now();
      final today =
          "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";

      // Create filter for today's attendance
      final filter = AttendanceFilter(
        dateFrom: today,
        dateTo: today,
        perPage: 100, // Set a high number to get all records
        page: 1,
      );

      // Fetch attendance history
      final attendanceResponse = await _attendanceService.getAttendanceHistory(
        token,
        filter,
        () async => true, // Placeholder for refresh token function
      );

      // Fetch active users count
      final activeUsers = await _attendanceService.getActiveUsers(token);

      // Calculate present and absent counts
      _presentCount = attendanceResponse.list.length;
      _totalActiveUsers = activeUsers.activeUsers;

      // Ensure we have valid numbers
      if (_totalActiveUsers < 0) {
        _totalActiveUsers = 0;
      }
      if (_presentCount < 0) {
        _presentCount = 0;
      }

      // Calculate absent count
      _absentCount = _totalActiveUsers - _presentCount;
      if (_absentCount < 0) {
        _absentCount = 0;
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = e.toString();
      // Reset counts on error
      _presentCount = 0;
      _absentCount = 0;
      _totalActiveUsers = 0;
      notifyListeners();
    }
  }
}
