import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/attendance_history_model.dart';
import '../models/attendance_filter_model.dart';
import '../models/error_response_model.dart';
import '../models/check_in_request_model.dart';
import '../models/check_in_response_model.dart';
import '../models/check_out_request_model.dart';
import '../models/active_user_model.dart';
import '../services/device_info_service.dart';
import '../../utils/constants.dart';

/// A version of AttendanceService that doesn't require BuildContext
/// This helps avoid the "Don't use BuildContext across async gaps" warning
class AttendanceServiceV2 {
  static const String _attendanceUuidKey = 'current_attendance_uuid';

  /// Get attendance history with token instead of BuildContext
  Future<AttendanceApiResponse> getAttendanceHistory(
    String token,
    AttendanceFilter filter,
    Future<bool> Function() refreshToken,
  ) async {
    if (token.isEmpty) {
      throw Exception('No authentication token available');
    }

    try {
      var response = await http
          .post(
            Uri.parse(getAttHistory),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: jsonEncode(filter.toJson()),
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw TimeoutException('Request timed out');
            },
          );

      if (response.statusCode == 401) {
        final refreshSuccess = await refreshToken();
        if (!refreshSuccess) {
          throw Exception('Session expired. Please login again.');
        }

        // Get the new token
        token = await _getNewToken();

        // Retry with new token
        response = await http.post(
          Uri.parse(getAttHistory),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: jsonEncode(filter.toJson()),
        );
      }

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final data = responseData['data'];
        if (data == null) {
          return AttendanceApiResponse(
            isAdmin: false,
            list: [],
            pagination: PaginationInfo.fromJson({}),
          );
        }
        return AttendanceApiResponse.fromJson(data);
      } else {
        // Prefer fromJson if the structure matches, otherwise construct manually
        if (responseData is Map<String, dynamic> &&
            responseData.containsKey('message')) {
          // If the response is a map and contains 'message', use fromJson
          // This assumes ErrorResponseModel.fromJson can handle the structure of responseData
          throw ErrorResponseModel.fromJson(responseData);
        } else {
          // Fallback if the structure is not as expected for fromJson
          throw ErrorResponseModel(
            status: 'error',
            message: responseData.toString(),
            code: response.statusCode.toString(),
          );
        }
      }
    } catch (e) {
      if (e is ErrorResponseModel) {
        rethrow;
      }
      throw Exception('Failed to fetch attendance history: ${e.toString()}');
    }
  }

  /// Get active users with token instead of BuildContext
  Future<ActiveUserModel> getActiveUsers(String token) async {
    final response = await http.get(
      Uri.parse(activeUsers),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      final responseData = jsonDecode(response.body);
      // Check if the response has a data field
      if (responseData is Map<String, dynamic> &&
          responseData.containsKey('data')) {
        return ActiveUserModel.fromJson(responseData['data']);
      }
      // If no data field, try to parse the response directly
      return ActiveUserModel.fromJson(responseData);
    } else {
      throw ErrorResponseModel.fromJson(jsonDecode(response.body));
    }
  }

  /// Check in with token instead of BuildContext
  Future<CheckInResponse> checkIn(
    String token,
    CheckInRequest request,
    Future<bool> Function() refreshToken,
  ) async {
    if (token.isEmpty) {
      throw Exception('No authentication token available');
    }

    try {
      var response = await http
          .post(
            Uri.parse(checkInAtt),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: jsonEncode(request.toJson()),
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw TimeoutException('Request timed out');
            },
          );

      if (response.statusCode == 401) {
        final refreshSuccess = await refreshToken();
        if (!refreshSuccess) {
          throw Exception('Session expired. Please login again.');
        }

        // Get the new token
        token = await _getNewToken();

        // Retry with new token
        response = await http.post(
          Uri.parse(checkInAtt),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: jsonEncode(request.toJson()),
        );
      }

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 201) {
        final checkInResponse = CheckInResponse.fromJson(responseData['data']);

        // Save attendance UUID to SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(
          _attendanceUuidKey,
          checkInResponse.attendanceUuid,
        );

        return checkInResponse;
      } else if (response.statusCode == 409) {
        // Handle the case where user is already checked in
        final errorModel = ErrorResponseModel.fromJson(responseData);

        // If the error code is ALREADY_CHECKED_IN, we need to handle it specially
        if (errorModel.code == 'ALREADY_CHECKED_IN') {
          // Get the attendance UUID from the error response if available
          String? attendanceUuid = responseData['data']?['attendance_uuid'];

          if (attendanceUuid != null) {
            // Save the attendance UUID to SharedPreferences
            final prefs = await SharedPreferences.getInstance();
            await prefs.setString(_attendanceUuidKey, attendanceUuid);

            // Return a response with the attendance UUID
            return CheckInResponse(attendanceUuid: attendanceUuid);
          }
        }

        // If we couldn't handle it specially, rethrow the error
        throw errorModel;
      } else {
        throw ErrorResponseModel.fromJson(responseData);
      }
    } catch (e) {
      if (e is ErrorResponseModel) {
        rethrow;
      }
      throw Exception('Failed to check in: $e');
    }
  }

  /// Get current attendance UUID
  Future<String?> getCurrentAttendanceUuid() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_attendanceUuidKey);
  }

  /// Clear current attendance UUID
  Future<void> clearCurrentAttendanceUuid() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_attendanceUuidKey);
  }

  /// Check out with token instead of BuildContext
  Future<bool> checkOut(
    String token,
    CheckOutRequest request,
    Future<bool> Function() refreshToken,
  ) async {
    if (token.isEmpty) {
      throw Exception('No authentication token available');
    }

    try {
      var response = await http
          .post(
            Uri.parse(checkOutAtt),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: jsonEncode(request.toJson()),
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw TimeoutException('Request timed out');
            },
          );

      if (response.statusCode == 401) {
        final refreshSuccess = await refreshToken();
        if (!refreshSuccess) {
          throw Exception('Session expired. Please login again.');
        }

        // Get the new token
        token = await _getNewToken();

        // Retry with new token
        response = await http.post(
          Uri.parse(checkOutAtt),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: jsonEncode(request.toJson()),
        );
      }

      if (response.statusCode == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  // Perform silent check out in case the user forget to check out
  Future<void> performSilentCheckOut(String token) async {
    if (token.isEmpty) {
      throw Exception('No authentication token available');
    }

    // Get current date
    final currentDate = DateTime.now();
    final formattedDate =
        "${currentDate.year}-${currentDate.month.toString().padLeft(2, '0')}-${currentDate.day.toString().padLeft(2, '0')}";
    final time = "T18:00:00Z"; // Set to default check out time 6:00 PM
    final timestamp = formattedDate + time;

    // Get device info
    final deviceInfo = await DeviceInfoService.getDeviceInfo();

    try {
      var response = await http
          .post(
            Uri.parse(checkOutAtt),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: jsonEncode({
              'locationId': 1,
              'coordinates': {'latitude': 5.843872, 'longitude': 118.066611},
              'accuracy': 50.0,
              'deviceInfo': deviceInfo.toJson(),
              'timestamp': timestamp,
            }),
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw TimeoutException('Request timed out');
            },
          );

      if (response.statusCode == 401) {
        // Get the new token
        token = await _getNewToken();

        // Retry with new token
        response = await http.post(
          Uri.parse(checkOutAtt),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: jsonEncode({
            'locationId': 1,
            'coordinates': {'latitude': 5.843872, 'longitude': 118.066611},
            'accuracy': 50.0,
            'deviceInfo': deviceInfo.toJson(),
            'timestamp': timestamp,
          }),
        );
      }
    } catch (e) {
      // Handle the error
    }
  }

  /// Helper method to get the new token from SharedPreferences
  Future<String> _getNewToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token') ?? '';
  }

  DeviceInfoService deviceInfoService = DeviceInfoService();
}
